# 安装指南 - Unescape & Format Chrome 插件

## 快速安装步骤

### 1. 准备图标文件

首先需要生成图标文件：

1. 在浏览器中打开 `create_simple_icons.html`
2. 页面会自动生成并下载4个图标文件
3. 将下载的图标文件放入 `icons/` 文件夹中：
   - `icon16.png`
   - `icon32.png` 
   - `icon48.png`
   - `icon128.png`

### 2. 安装插件到Chrome

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 在右上角开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择这个项目的文件夹
6. 插件安装完成！

### 3. 测试插件功能

#### 测试Markdown功能：
1. 在浏览器中打开 `test_pages/markdown_test.html`
2. 点击Chrome工具栏中的插件图标
3. 查看插件是否检测到Markdown内容
4. 点击"Toggle Format"按钮查看格式化效果

#### 测试JSON功能：
1. 在浏览器中打开 `test_pages/json_test.html`
2. 点击插件图标
3. 查看插件是否检测到JSON内容
4. 点击"Toggle Format"按钮查看格式化效果

## 功能说明

### 自动检测
插件会自动检测页面中包含转义字符的内容：
- **Markdown内容**：检测 `\\n`, `\\*`, `\\[` 等转义字符
- **JSON内容**：检测 `\"`, `\\n`, `\\t` 等转义字符

### 手动控制
- 点击插件图标查看当前状态
- 使用"Toggle Format"按钮切换显示模式
- 使用"Refresh Page"按钮重新加载页面

### 支持的转义字符
- `\\n` → 换行符
- `\\t` → 制表符  
- `\\r` → 回车符
- `\"` → 双引号
- `\'` → 单引号
- `\\\\` → 反斜杠

## 故障排除

### 插件无法工作
1. 确认插件已在 `chrome://extensions/` 中启用
2. 检查页面是否包含可检测的转义内容
3. 尝试刷新页面
4. 查看浏览器控制台是否有错误信息

### 内容未被检测
插件寻找特定的模式：
- **Markdown**：必须包含转义字符如 `\\n`, `\\*` 等
- **JSON**：必须是有效的JSON结构且包含转义字符

### 性能问题
- 插件只处理内容长度超过50个字符的页面
- 大型内容可能需要一些时间来处理
- 如果遇到问题，可以使用切换功能返回原始内容

## 开发说明

如果你想修改插件：

1. 编辑源文件
2. 在 `chrome://extensions/` 中重新加载插件
3. 在测试页面上测试更改

主要文件：
- `content.js` - 内容检测和处理逻辑
- `popup.html/js` - 插件弹窗界面
- `styles.css` - 格式化内容的样式
- `manifest.json` - 插件配置
