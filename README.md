# Unescape & Format Chrome Extension

A Chrome extension that automatically detects and formats escaped Markdown and JSON content on web pages.

## Features

- 🔧 **Automatic Detection**: Automatically detects Markdown and JSON content with escape characters
- 📝 **Markdown Formatting**: Converts escaped Markdown to beautifully formatted HTML
- 📊 **JSON Formatting**: Formats escaped JSON with proper indentation and syntax highlighting
- 🔄 **Toggle View**: Switch between original and formatted content with one click
- 🎨 **Beautiful UI**: Clean, modern interface with gradient styling
- 🌙 **Dark Mode**: Automatic dark mode support based on system preferences

## Installation

### From Source (Development)

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right corner
4. Click "Load unpacked" and select the extension directory
5. The extension icon should appear in your Chrome toolbar

### Generate Icons (Optional)

If you want to generate custom icons:

1. Open `generate_icons.html` in your browser
2. Right-click each canvas and save as `icon16.png`, `icon32.png`, `icon48.png`, and `icon128.png`
3. Place the saved files in the `icons/` directory

## Usage

### Automatic Detection

The extension automatically runs on all web pages and detects:

- **Markdown content** with escape characters like `\\n`, `\\*`, `\\[`, etc.
- **JSON content** with escaped quotes, newlines, and other characters

### Manual Control

1. Click the extension icon in the Chrome toolbar
2. View the current status and content type detected
3. Use the "Toggle Format" button to switch between original and formatted views
4. Use "Refresh Page" to reload the page and re-detect content

### Test Pages

Use the included test pages to verify the extension works:

- Open `test_pages/markdown_test.html` to test Markdown formatting
- Open `test_pages/json_test.html` to test JSON formatting

## Supported Content Types

### Markdown

The extension detects and formats:
- Headers (`# ## ###`)
- Bold and italic text (`**bold**`, `*italic*`)
- Links (`[text](url)`)
- Code blocks and inline code
- Lists (ordered and unordered)
- Blockquotes
- Tables
- Horizontal rules

### JSON

The extension detects and formats:
- Escaped quotes (`\"`)
- Escaped newlines (`\\n`)
- Escaped tabs (`\\t`)
- Escaped backslashes (`\\\\`)
- Complex nested objects and arrays

## File Structure

```
chrome-plugin-unescape-1/
├── manifest.json          # Extension configuration
├── content.js            # Main content script
├── popup.html           # Extension popup interface
├── popup.js             # Popup functionality
├── background.js        # Background service worker
├── styles.css           # Styling for formatted content
├── libs/
│   └── marked.min.js    # Markdown parsing library
├── icons/
│   ├── icon16.png       # 16x16 icon
│   ├── icon32.png       # 32x32 icon
│   ├── icon48.png       # 48x48 icon
│   └── icon128.png      # 128x128 icon
├── test_pages/
│   ├── markdown_test.html # Markdown test page
│   └── json_test.html     # JSON test page
└── README.md            # This file
```

## Technical Details

### Manifest V3

This extension uses Chrome's Manifest V3 for better security and performance:
- Service worker instead of background pages
- Declarative content scripts
- Modern permissions model

### Content Script

The content script (`content.js`) handles:
- Page content detection
- Escape character processing
- Content formatting and rendering
- Toggle functionality

### Libraries Used

- **marked.js**: For Markdown parsing and HTML generation
- **Native Chrome APIs**: For extension functionality

## Development

### Prerequisites

- Chrome browser (latest version recommended)
- Basic knowledge of JavaScript, HTML, and CSS

### Making Changes

1. Edit the source files as needed
2. Reload the extension in `chrome://extensions/`
3. Test on the provided test pages or real web content

### Adding New Features

The extension is designed to be extensible:
- Add new content type detection in `content.js`
- Extend the popup interface in `popup.html` and `popup.js`
- Add new styling in `styles.css`

## Troubleshooting

### Extension Not Working

1. Check that the extension is enabled in `chrome://extensions/`
2. Verify the page contains detectable escaped content
3. Try refreshing the page
4. Check the browser console for error messages

### Content Not Detected

The extension looks for specific patterns:
- Markdown: Must have escape characters like `\\n`, `\\*`, etc.
- JSON: Must be valid JSON structure with escape characters

### Performance Issues

- The extension only processes pages with sufficient content (50+ characters)
- Large content may take a moment to process
- Use the toggle feature to switch back to original content if needed

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.
