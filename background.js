// Background script for Unescape & Format Chrome Extension

// Install/update handler
chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('Unescape & Format extension installed');
    
    // Set default settings
    chrome.storage.sync.set({
      autoDetect: true,
      enableMarkdown: true,
      enableJson: true,
      showNotifications: true
    });
  } else if (details.reason === 'update') {
    console.log('Unescape & Format extension updated');
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // This will open the popup, no additional action needed
  console.log('Extension icon clicked for tab:', tab.id);
});

// Listen for tab updates to potentially re-inject content script
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Skip chrome:// and extension pages
    if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
      return;
    }
    
    // Content script will be automatically injected due to manifest configuration
    console.log('Tab updated:', tabId, tab.url);
  }
});

// Handle messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'contentProcessed') {
    console.log('Content processed on tab:', sender.tab.id, 'Type:', request.contentType);
    
    // Optionally show notification
    chrome.storage.sync.get(['showNotifications'], (result) => {
      if (result.showNotifications) {
        chrome.notifications.create({
          type: 'basic',
          iconUrl: 'icons/icon48.png',
          title: 'Content Processed',
          message: `${request.contentType} content has been formatted`
        });
      }
    });
  }
  
  return true; // Keep message channel open for async response
});

// Context menu (optional feature for future enhancement)
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'unescapeFormat',
    title: 'Unescape & Format Selection',
    contexts: ['selection']
  });
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'unescapeFormat') {
    // Send message to content script to process selected text
    chrome.tabs.sendMessage(tab.id, {
      action: 'processSelection',
      selectedText: info.selectionText
    });
  }
});
