// Chrome Extension: Unescape & Format
// Content Script for detecting and processing Markdown/JSON content

class UnescapeFormatter {
  constructor() {
    this.isEnabled = true;
    this.originalContent = null;
    this.processedContent = null;
    this.contentType = null;
    this.targetElement = null;

    this.init();
  }

  init() {
    // Wait for page to load completely
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.detectAndProcess());
    } else {
      this.detectAndProcess();
    }

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'toggle') {
        this.toggle();
        sendResponse({ success: true });
      } else if (request.action === 'getStatus') {
        const containers = document.querySelectorAll('.unescape-formatter-container');
        sendResponse({
          isEnabled: containers.length > 0,
          contentType: this.contentType || 'none',
          hasContent: containers.length > 0
        });
      }
    });
  }

  detectAndProcess() {
    // Find and process all potential content elements
    this.processAllContentElements();
  }

  processAllContentElements() {
    const selectors = [
      'pre',
      'code',
      '.test-content'
    ];

    let processedCount = 0;

    // Process all matching elements, not just the first one
    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        const text = element.innerText || element.textContent || '';

        // Skip if already processed or too short
        if (element.classList.contains('unescape-formatter-container') || text.length < 50) {
          continue;
        }

        console.log(`Checking element ${selector}:`, text.substring(0, 100) + '...');

        // Check JSON first as it's more specific
        if (this.isJsonContent(text)) {
          console.log('✅ Detected JSON content');
          this.processElementAsJson(element);
          processedCount++;
        } else if (this.isMarkdownContent(text)) {
          console.log('✅ Detected Markdown content');
          this.processElementAsMarkdown(element);
          processedCount++;
        }
      }
    }

    // Set the content type based on what we found
    if (processedCount > 0) {
      this.contentType = 'mixed';
      console.log(`✅ Processed ${processedCount} content elements`);
    } else {
      console.log('❌ No escapable content detected');
    }
  }

  processElementAsMarkdown(element) {
    const originalContent = element.innerHTML;
    const rawText = element.innerText || element.textContent;
    const unescapedText = this.unescapeText(rawText);

    try {
      // Use marked.js to parse markdown
      const htmlContent = marked.parse(unescapedText);

      const processedContent = `
        <div class="unescape-formatter-container">
          <div class="unescape-formatter-header">
            <span class="unescape-formatter-badge">Markdown Formatted</span>
            <button class="unescape-formatter-toggle" onclick="window.unescapeFormatter.toggleElement(this)">
              Show Original
            </button>
          </div>
          <div class="unescape-formatter-content markdown-content">
            ${htmlContent}
          </div>
        </div>
      `;

      // Store original content as data attribute
      element.setAttribute('data-original-content', originalContent);
      element.innerHTML = processedContent;
    } catch (error) {
      console.error('Error processing markdown:', error);
    }
  }

  processElementAsJson(element) {
    const originalContent = element.innerHTML;
    const rawText = element.innerText || element.textContent;
    const unescapedText = this.unescapeText(rawText);

    try {
      // Parse and format JSON
      const jsonObj = JSON.parse(unescapedText);
      const formattedJson = JSON.stringify(jsonObj, null, 2);

      const processedContent = `
        <div class="unescape-formatter-container">
          <div class="unescape-formatter-header">
            <span class="unescape-formatter-badge">JSON Formatted</span>
            <button class="unescape-formatter-toggle" onclick="window.unescapeFormatter.toggleElement(this)">
              Show Original
            </button>
          </div>
          <pre class="unescape-formatter-content json-content"><code>${this.escapeHtml(formattedJson)}</code></pre>
        </div>
      `;

      // Store original content as data attribute
      element.setAttribute('data-original-content', originalContent);
      element.innerHTML = processedContent;
    } catch (error) {
      console.error('Error processing JSON:', error);
    }
  }

  isMarkdownContent(text) {
    // Don't treat JSON as Markdown even if it has escape characters
    if (this.isJsonContent(text)) {
      return false;
    }

    // Check for common Markdown patterns with escape characters
    const markdownPatterns = [
      /\\#\s+/,  // Escaped headers
      /\\\*\\\*/,  // Escaped bold
      /\\\[.*\\\]\\\(.*\\\)/,  // Escaped links
      /\\`.*\\`/,  // Escaped code
      /\\>/,  // Escaped blockquotes
      /#\s+.*\\n/,  // Headers with escaped newlines
      /\*\*.*\*\*.*\\n/,  // Bold text with escaped newlines
      /\\\*/,  // Any escaped asterisk
      /\\\[/,  // Any escaped bracket
      /\\`/,   // Any escaped backtick
    ];

    // Also check for content that looks like escaped markdown
    const hasEscapedNewlines = /\\n/.test(text);
    const hasMarkdownStructure = /#\s+|^\*\*|\[.*\]\(|\`.*\`|^\s*[-*+]\s+|^\s*\d+\.\s+/.test(text);

    return markdownPatterns.some(pattern => pattern.test(text)) ||
           (hasEscapedNewlines && hasMarkdownStructure);
  }

  isJsonContent(text) {
    // Check for JSON patterns with escape characters
    const jsonPatterns = [
      /\\\"/g,  // Escaped quotes
      /\\n/g,   // Escaped newlines
      /\\t/g,   // Escaped tabs
      /\\r/g,   // Escaped carriage returns
      /\\\\/g,  // Escaped backslashes
    ];
    
    // Must have JSON structure and escape characters
    const hasJsonStructure = /^\s*[\{\[]/.test(text.trim()) && /[\}\]]\s*$/.test(text.trim());
    const hasEscapeChars = jsonPatterns.some(pattern => pattern.test(text));
    
    return hasJsonStructure && hasEscapeChars;
  }

  findContentElement() {
    // Try to find the main content element
    const selectors = [
      'pre',
      'code',
      'body > div:only-child',
      'body > p:only-child',
      'main',
      '.content',
      '#content',
      'article'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && element.innerText.length > 50) {
        return element;
      }
    }

    // Fallback to body if no specific element found
    return document.body;
  }

  unescapeText(text) {
    // Process escape characters in the correct order
    console.log('🔍 Processing:', text.substring(0, 100) + '...');

    let result = text;

    // Step 1: Handle double backslashes first to preserve them temporarily
    result = result.replace(/\\\\/g, '___DOUBLE_BACKSLASH___');

    // Step 2: Handle all other escape sequences
    result = result
      // Common escape sequences
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\r/g, '\r')
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'")

      // Markdown-specific escapes
      .replace(/\\`/g, '`')
      .replace(/\\#/g, '#')
      .replace(/\\\*/g, '*')
      .replace(/\\\[/g, '[')
      .replace(/\\\]/g, ']')
      .replace(/\\\(/g, '(')
      .replace(/\\\)/g, ')')
      .replace(/\\>/g, '>')
      .replace(/\\-/g, '-')
      .replace(/\\=/g, '=')
      .replace(/\\!/g, '!')
      .replace(/\\&/g, '&')
      .replace(/\\%/g, '%')
      .replace(/\\\$/g, '$')
      .replace(/\\@/g, '@')
      .replace(/\\\^/g, '^')
      .replace(/\\~/g, '~')
      .replace(/\\\+/g, '+')
      .replace(/\\\|/g, '|')
      .replace(/\\{/g, '{')
      .replace(/\\}/g, '}')
      .replace(/\\:/g, ':')
      .replace(/\\;/g, ';')
      .replace(/\\,/g, ',')
      .replace(/\\./g, '.')
      .replace(/\\\?/g, '?');

    // Step 3: Remove any remaining single backslashes that appear to be escape attempts
    result = result.replace(/\\(?![a-zA-Z0-9])/g, '');

    // Step 4: Restore double backslashes as single backslashes
    result = result.replace(/___DOUBLE_BACKSLASH___/g, '\\');

    console.log('✅ Result:', result.substring(0, 100) + '...');
    return result;
  }

  processMarkdown() {
    if (!this.targetElement) return;

    this.originalContent = this.targetElement.innerHTML;
    const rawText = this.targetElement.innerText || this.targetElement.textContent;
    const unescapedText = this.unescapeText(rawText);

    try {
      // Use marked.js to parse markdown
      const htmlContent = marked.parse(unescapedText);
      
      this.processedContent = `
        <div class="unescape-formatter-container">
          <div class="unescape-formatter-header">
            <span class="unescape-formatter-badge">Markdown Formatted</span>
            <button class="unescape-formatter-toggle" onclick="window.unescapeFormatter.toggle()">
              Show Original
            </button>
          </div>
          <div class="unescape-formatter-content markdown-content">
            ${htmlContent}
          </div>
        </div>
      `;

      if (this.isEnabled) {
        this.targetElement.innerHTML = this.processedContent;
      }
    } catch (error) {
      console.error('Error processing markdown:', error);
    }
  }

  processJson() {
    if (!this.targetElement) return;

    this.originalContent = this.targetElement.innerHTML;
    const rawText = this.targetElement.innerText || this.targetElement.textContent;
    const unescapedText = this.unescapeText(rawText);

    try {
      // Parse and format JSON
      const jsonObj = JSON.parse(unescapedText);
      const formattedJson = JSON.stringify(jsonObj, null, 2);
      
      this.processedContent = `
        <div class="unescape-formatter-container">
          <div class="unescape-formatter-header">
            <span class="unescape-formatter-badge">JSON Formatted</span>
            <button class="unescape-formatter-toggle" onclick="window.unescapeFormatter.toggle()">
              Show Original
            </button>
          </div>
          <pre class="unescape-formatter-content json-content"><code>${this.escapeHtml(formattedJson)}</code></pre>
        </div>
      `;

      if (this.isEnabled) {
        this.targetElement.innerHTML = this.processedContent;
      }
    } catch (error) {
      console.error('Error processing JSON:', error);
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  toggle() {
    // Toggle all processed elements
    const containers = document.querySelectorAll('.unescape-formatter-container');
    containers.forEach(container => {
      const button = container.querySelector('.unescape-formatter-toggle');
      if (button) {
        this.toggleElement(button);
      }
    });
  }

  toggleElement(button) {
    const container = button.closest('.unescape-formatter-container');
    if (!container) return;

    const parentElement = container.parentElement;
    const originalContent = parentElement.getAttribute('data-original-content');

    if (!originalContent) return;

    const isShowingFormatted = button.textContent.includes('Show Original');

    if (isShowingFormatted) {
      // Switch to original
      parentElement.innerHTML = originalContent;
    } else {
      // Switch back to formatted - need to re-process
      const text = originalContent.replace(/<[^>]*>/g, ''); // Strip HTML tags to get text
      if (this.isMarkdownContent(text)) {
        this.processElementAsMarkdown(parentElement);
      } else if (this.isJsonContent(text)) {
        this.processElementAsJson(parentElement);
      }
    }
  }
}

// Initialize the formatter
window.unescapeFormatter = new UnescapeFormatter();
