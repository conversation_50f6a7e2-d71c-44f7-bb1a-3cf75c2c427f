// Chrome Extension: Unescape & Format
// Content Script for detecting and processing Markdown/JSON content

class UnescapeFormatter {
  constructor() {
    this.isEnabled = true;
    this.originalContent = null;
    this.processedContent = null;
    this.contentType = null;
    this.targetElement = null;
    
    this.init();
  }

  init() {
    // Wait for page to load completely
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.detectAndProcess());
    } else {
      this.detectAndProcess();
    }

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'toggle') {
        this.toggle();
        sendResponse({ success: true });
      } else if (request.action === 'getStatus') {
        sendResponse({ 
          isEnabled: this.isEnabled,
          contentType: this.contentType,
          hasContent: !!this.originalContent
        });
      }
    });
  }

  detectAndProcess() {
    const bodyText = document.body.innerText || document.body.textContent || '';
    
    // Skip if content is too short
    if (bodyText.length < 50) return;

    // Detect content type and find target element
    if (this.isMarkdownContent(bodyText)) {
      this.contentType = 'markdown';
      this.targetElement = this.findContentElement();
      if (this.targetElement) {
        this.processMarkdown();
      }
    } else if (this.isJsonContent(bodyText)) {
      this.contentType = 'json';
      this.targetElement = this.findContentElement();
      if (this.targetElement) {
        this.processJson();
      }
    }
  }

  isMarkdownContent(text) {
    // Check for common Markdown patterns with escape characters
    const markdownPatterns = [
      /\\#\s+/,  // Escaped headers
      /\\\*\\\*/,  // Escaped bold
      /\\\[.*\\\]\\\(.*\\\)/,  // Escaped links
      /\\`.*\\`/,  // Escaped code
      /\\>/,  // Escaped blockquotes
      /\\\n/g,  // Escaped newlines
      /#\s+.*\\n/,  // Headers with escaped newlines
      /\*\*.*\*\*.*\\n/,  // Bold text with escaped newlines
    ];
    
    return markdownPatterns.some(pattern => pattern.test(text));
  }

  isJsonContent(text) {
    // Check for JSON patterns with escape characters
    const jsonPatterns = [
      /\\\"/g,  // Escaped quotes
      /\\n/g,   // Escaped newlines
      /\\t/g,   // Escaped tabs
      /\\r/g,   // Escaped carriage returns
      /\\\\/g,  // Escaped backslashes
    ];
    
    // Must have JSON structure and escape characters
    const hasJsonStructure = /^\s*[\{\[]/.test(text.trim()) && /[\}\]]\s*$/.test(text.trim());
    const hasEscapeChars = jsonPatterns.some(pattern => pattern.test(text));
    
    return hasJsonStructure && hasEscapeChars;
  }

  findContentElement() {
    // Try to find the main content element
    const selectors = [
      'pre',
      'code',
      'body > div:only-child',
      'body > p:only-child',
      'main',
      '.content',
      '#content',
      'article'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element && element.innerText.length > 50) {
        return element;
      }
    }

    // Fallback to body if no specific element found
    return document.body;
  }

  unescapeText(text) {
    return text
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\r/g, '\r')
      .replace(/\\"/g, '"')
      .replace(/\\'/g, "'")
      .replace(/\\\\/g, '\\');
  }

  processMarkdown() {
    if (!this.targetElement) return;

    this.originalContent = this.targetElement.innerHTML;
    const rawText = this.targetElement.innerText || this.targetElement.textContent;
    const unescapedText = this.unescapeText(rawText);

    try {
      // Use marked.js to parse markdown
      const htmlContent = marked.parse(unescapedText);
      
      this.processedContent = `
        <div class="unescape-formatter-container">
          <div class="unescape-formatter-header">
            <span class="unescape-formatter-badge">Markdown Formatted</span>
            <button class="unescape-formatter-toggle" onclick="window.unescapeFormatter.toggle()">
              Show Original
            </button>
          </div>
          <div class="unescape-formatter-content markdown-content">
            ${htmlContent}
          </div>
        </div>
      `;

      if (this.isEnabled) {
        this.targetElement.innerHTML = this.processedContent;
      }
    } catch (error) {
      console.error('Error processing markdown:', error);
    }
  }

  processJson() {
    if (!this.targetElement) return;

    this.originalContent = this.targetElement.innerHTML;
    const rawText = this.targetElement.innerText || this.targetElement.textContent;
    const unescapedText = this.unescapeText(rawText);

    try {
      // Parse and format JSON
      const jsonObj = JSON.parse(unescapedText);
      const formattedJson = JSON.stringify(jsonObj, null, 2);
      
      this.processedContent = `
        <div class="unescape-formatter-container">
          <div class="unescape-formatter-header">
            <span class="unescape-formatter-badge">JSON Formatted</span>
            <button class="unescape-formatter-toggle" onclick="window.unescapeFormatter.toggle()">
              Show Original
            </button>
          </div>
          <pre class="unescape-formatter-content json-content"><code>${this.escapeHtml(formattedJson)}</code></pre>
        </div>
      `;

      if (this.isEnabled) {
        this.targetElement.innerHTML = this.processedContent;
      }
    } catch (error) {
      console.error('Error processing JSON:', error);
    }
  }

  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  toggle() {
    if (!this.targetElement || !this.originalContent || !this.processedContent) return;

    this.isEnabled = !this.isEnabled;
    
    if (this.isEnabled) {
      this.targetElement.innerHTML = this.processedContent;
    } else {
      this.targetElement.innerHTML = this.originalContent;
    }

    // Update toggle button text
    const toggleBtn = document.querySelector('.unescape-formatter-toggle');
    if (toggleBtn) {
      toggleBtn.textContent = this.isEnabled ? 'Show Original' : 'Show Formatted';
    }
  }
}

// Initialize the formatter
window.unescapeFormatter = new UnescapeFormatter();
