#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create simple PNG icons for the Chrome extension
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    # Create a new image with transparent background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw a gradient-like background circle
    center = size // 2
    radius = size // 2 - 2
    
    # Draw outer circle with gradient effect
    for i in range(radius, 0, -1):
        alpha = int(255 * (radius - i) / radius)
        color = (102, 126, 234, alpha)  # Blue gradient
        draw.ellipse([center - i, center - i, center + i, center + i], fill=color)
    
    # Draw inner content - a stylized "U" for Unescape
    font_size = max(8, size // 4)
    try:
        # Try to use a system font
        font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
        except:
            font = ImageFont.load_default()
    
    # Draw the "U" symbol
    text = "🔧"
    if size >= 32:
        text = "🔧"
    else:
        text = "U"
    
    # Get text bounding box
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # Center the text
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    # Draw white text with shadow
    draw.text((x + 1, y + 1), text, fill=(0, 0, 0, 128), font=font)  # Shadow
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)    # Main text
    
    # Save the image
    img.save(filename, 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    # Create icons directory if it doesn't exist
    os.makedirs('icons', exist_ok=True)
    
    # Create icons in different sizes
    sizes = [16, 32, 48, 128]
    
    for size in sizes:
        filename = f'icons/icon{size}.png'
        create_icon(size, filename)

if __name__ == '__main__':
    main()
