<!DOCTYPE html>
<html>
<head>
    <title>Simple Icon Creator</title>
</head>
<body>
    <h1>Creating Icons...</h1>
    <div id="output"></div>

    <script>
        function createIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // Draw circle background
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Draw white border
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Draw text
            ctx.fillStyle = '#fff';
            ctx.font = `bold ${Math.floor(size/4)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('U', size/2, size/2);
            
            return canvas.toDataURL('image/png');
        }

        // Create icons and download links
        const sizes = [16, 32, 48, 128];
        const output = document.getElementById('output');
        
        sizes.forEach(size => {
            const dataUrl = createIcon(size);
            
            const link = document.createElement('a');
            link.href = dataUrl;
            link.download = `icon${size}.png`;
            link.textContent = `Download icon${size}.png`;
            link.style.display = 'block';
            link.style.margin = '10px 0';
            
            const img = document.createElement('img');
            img.src = dataUrl;
            img.style.width = size + 'px';
            img.style.height = size + 'px';
            img.style.marginRight = '10px';
            
            const div = document.createElement('div');
            div.appendChild(img);
            div.appendChild(link);
            
            output.appendChild(div);
        });
        
        // Auto-download all icons
        setTimeout(() => {
            const links = output.querySelectorAll('a');
            links.forEach((link, index) => {
                setTimeout(() => link.click(), index * 500);
            });
        }, 1000);
    </script>
</body>
</html>
