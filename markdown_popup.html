<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Preview</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0;
            min-height: 600px;
        }
        
        .input-section, .output-section {
            padding: 20px;
        }
        
        .input-section {
            border-right: 1px solid #e0e0e0;
            background: #fafafa;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #4caf50;
        }
        
        .input-textarea {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            background: white;
        }
        
        .output-content {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            min-height: 400px;
            background: white;
            overflow-y: auto;
        }
        
        .controls {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }
        
        .btn-secondary:hover {
            background: #e0e0e0;
        }
        
        /* Markdown styles */
        .markdown-content h1, .markdown-content h2, .markdown-content h3,
        .markdown-content h4, .markdown-content h5, .markdown-content h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        .markdown-content h1 {
            font-size: 2em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 10px;
        }
        
        .markdown-content h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }
        
        .markdown-content h3 {
            font-size: 1.25em;
        }
        
        .markdown-content p {
            margin-bottom: 16px;
            line-height: 1.6;
        }
        
        .markdown-content code {
            background: #f6f8fa;
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        .markdown-content pre {
            background: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .markdown-content pre code {
            background: transparent;
            border-radius: 0;
            font-size: 100%;
            margin: 0;
            padding: 0;
        }
        
        .markdown-content ul, .markdown-content ol {
            margin-bottom: 16px;
            padding-left: 2em;
        }
        
        .markdown-content li {
            margin-bottom: 4px;
        }
        
        .markdown-content table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin-bottom: 16px;
        }
        
        .markdown-content table th,
        .markdown-content table td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }
        
        .markdown-content table th {
            background: #f6f8fa;
            font-weight: 600;
        }
        
        .markdown-content blockquote {
            border-left: 4px solid #dfe2e5;
            padding: 0 16px;
            color: #6a737d;
            margin: 0 0 16px 0;
        }
        
        .stats {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Markdown Unescape & Preview</h1>
            <p>Test and preview escaped Markdown content</p>
        </div>
        
        <div class="content">
            <div class="input-section">
                <div class="section-title">
                    <span class="status-indicator"></span>
                    Input (Escaped Markdown)
                </div>
                
                <div class="controls">
                    <button class="btn btn-primary" onclick="processMarkdown()">🔄 Process</button>
                    <button class="btn btn-secondary" onclick="loadTestData()">📝 Load Test Data</button>
                    <button class="btn btn-secondary" onclick="clearAll()">🗑️ Clear</button>
                </div>
                
                <textarea 
                    id="input" 
                    class="input-textarea" 
                    placeholder="Paste your escaped Markdown here...&#10;&#10;Example:&#10;# Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2"
                ></textarea>
                
                <div class="stats" id="inputStats">
                    Ready to process...
                </div>
            </div>
            
            <div class="output-section">
                <div class="section-title">
                    <span class="status-indicator"></span>
                    Output (Rendered Markdown)
                </div>
                
                <div class="controls">
                    <button class="btn btn-secondary" onclick="copyOutput()">📋 Copy HTML</button>
                    <button class="btn btn-secondary" onclick="downloadOutput()">💾 Download</button>
                </div>
                
                <div id="output" class="output-content markdown-content">
                    <p style="color: #666; text-align: center; margin-top: 100px;">
                        Enter escaped Markdown content and click "Process" to see the result.
                    </p>
                </div>
                
                <div class="stats" id="outputStats">
                    Waiting for input...
                </div>
            </div>
        </div>
    </div>

    <!-- Include marked.js for Markdown processing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <script>
        // Enhanced unescape function
        function unescapeText(text) {
            console.log('🔍 Processing text:', text.substring(0, 100) + '...');
            
            let result = text;
            
            // Step 1: Handle double backslashes first to preserve them temporarily
            result = result.replace(/\\\\/g, '___DOUBLE_BACKSLASH___');
            
            // Step 2: Handle all other escape sequences
            result = result
              // Common escape sequences
              .replace(/\\n/g, '\n')
              .replace(/\\t/g, '\t')
              .replace(/\\r/g, '\r')
              .replace(/\\"/g, '"')
              .replace(/\\'/g, "'")
              
              // Markdown-specific escapes
              .replace(/\\`/g, '`')
              .replace(/\\#/g, '#')
              .replace(/\\\*/g, '*')
              .replace(/\\\[/g, '[')
              .replace(/\\\]/g, ']')
              .replace(/\\\(/g, '(')
              .replace(/\\\)/g, ')')
              .replace(/\\>/g, '>')
              .replace(/\\-/g, '-')
              .replace(/\\=/g, '=')
              .replace(/\\!/g, '!')
              .replace(/\\&/g, '&')
              .replace(/\\%/g, '%')
              .replace(/\\\$/g, '$')
              .replace(/\\@/g, '@')
              .replace(/\\\^/g, '^')
              .replace(/\\~/g, '~')
              .replace(/\\\+/g, '+')
              .replace(/\\\|/g, '|')
              .replace(/\\{/g, '{')
              .replace(/\\}/g, '}')
              .replace(/\\:/g, ':')
              .replace(/\\;/g, ';')
              .replace(/\\,/g, ',')
              .replace(/\\./g, '.')
              .replace(/\\\?/g, '?');
            
            // Step 3: Remove any remaining single backslashes that appear to be escape attempts
            result = result.replace(/\\(?![a-zA-Z0-9])/g, '');
            
            // Step 4: Restore double backslashes as single backslashes
            result = result.replace(/___DOUBLE_BACKSLASH___/g, '\\');
            
            console.log('✅ Processed result:', result.substring(0, 100) + '...');
            return result;
        }

        function processMarkdown() {
            const input = document.getElementById('input').value;
            const output = document.getElementById('output');
            const inputStats = document.getElementById('inputStats');
            const outputStats = document.getElementById('outputStats');

            if (!input.trim()) {
                output.innerHTML = '<p style="color: #666; text-align: center;">Please enter some content to process.</p>';
                return;
            }

            try {
                // Debug: Check what we actually received
                console.log('🔍 Raw input:', JSON.stringify(input));

                // Step 1: Unescape the text
                const unescaped = unescapeText(input);
                console.log('🔍 After unescape:', JSON.stringify(unescaped));

                // Step 2: Convert to Markdown
                const html = marked.parse(unescaped);
                console.log('🔍 Generated HTML:', html);

                // Step 3: Display result
                output.innerHTML = html;
                
                // Update stats
                const escapeCount = (input.match(/\\./g) || []).length;
                const lineCount = unescaped.split('\n').length;
                
                inputStats.innerHTML = `
                    Length: ${input.length} chars | 
                    Escape sequences: ${escapeCount} | 
                    Lines: ${input.split('\\n').length}
                `;
                
                outputStats.innerHTML = `
                    Length: ${unescaped.length} chars | 
                    Lines: ${lineCount} | 
                    HTML elements: ${output.querySelectorAll('*').length}
                `;
                
                console.log('✅ Markdown processing completed successfully');
                
            } catch (error) {
                console.error('❌ Error processing markdown:', error);
                output.innerHTML = `
                    <div style="color: #d32f2f; padding: 20px; background: #ffebee; border-radius: 6px;">
                        <strong>Error processing Markdown:</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        function loadTestData() {
            const testData = [
                '# Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2',
                '### Code Example\\n\\n```javascript\\nfunction test() {\\n    console.log(\\\"Hello!\\\");\\n}\\n```',
                '## Features\\n\\n1. **Auto-detection**\\n2. **Multi-element processing**\\n3. **Better escape handling**\\n\\n> This should work now!\\n\\n| Col1 | Col2 |\\n|------|------|\\n| A    | B    |',
                '- Item 1\\n- Item 2\\n- Item 3\\'
            ];
            
            const randomTest = testData[Math.floor(Math.random() * testData.length)];
            document.getElementById('input').value = randomTest;
            processMarkdown();
        }

        function clearAll() {
            document.getElementById('input').value = '';
            document.getElementById('output').innerHTML = '<p style="color: #666; text-align: center; margin-top: 100px;">Enter escaped Markdown content and click "Process" to see the result.</p>';
            document.getElementById('inputStats').innerHTML = 'Ready to process...';
            document.getElementById('outputStats').innerHTML = 'Waiting for input...';
        }

        function copyOutput() {
            const output = document.getElementById('output');
            const html = output.innerHTML;
            
            navigator.clipboard.writeText(html).then(() => {
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = '✅ Copied!';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy:', err);
            });
        }

        function downloadOutput() {
            const output = document.getElementById('output');
            const html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Processed Markdown</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2 { border-bottom: 1px solid #eee; padding-bottom: 10px; }
        code { background: #f6f8fa; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f6f8fa; padding: 16px; border-radius: 6px; overflow: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f6f8fa; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 16px; color: #666; }
    </style>
</head>
<body>
${output.innerHTML}
</body>
</html>`;
            
            const blob = new Blob([html], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'processed-markdown.html';
            a.click();
            URL.revokeObjectURL(url);
        }

        // Auto-process on input change (with debounce)
        let debounceTimer;
        document.getElementById('input').addEventListener('input', () => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(processMarkdown, 500);
        });

        // Load initial test data
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Markdown Preview Tool loaded');
        });
    </script>
</body>
</html>
