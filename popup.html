<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Unescape & Format</title>
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .header {
      text-align: center;
      margin-bottom: 20px;
    }

    .header h1 {
      margin: 0 0 5px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .header p {
      margin: 0;
      font-size: 12px;
      opacity: 0.8;
    }

    .status-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .status-item:last-child {
      margin-bottom: 0;
    }

    .status-label {
      font-size: 14px;
      font-weight: 500;
    }

    .status-value {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
      background: rgba(255, 255, 255, 0.2);
    }

    .status-value.active {
      background: #4CAF50;
    }

    .status-value.inactive {
      background: #f44336;
    }

    .controls {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 10px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      text-align: center;
    }

    .btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .btn.primary {
      background: rgba(255, 255, 255, 0.9);
      color: #333;
    }

    .btn.primary:hover {
      background: white;
    }

    .info {
      font-size: 11px;
      text-align: center;
      opacity: 0.7;
      margin-top: 15px;
      line-height: 1.4;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 10px;
    }

    .spinner {
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top: 2px solid white;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔧 Unescape & Format</h1>
    <p>Process Markdown & JSON content</p>
  </div>

  <div class="status-card">
    <div class="status-item">
      <span class="status-label">Status:</span>
      <span class="status-value" id="status">Checking...</span>
    </div>
    <div class="status-item">
      <span class="status-label">Content Type:</span>
      <span class="status-value" id="contentType">None</span>
    </div>
    <div class="status-item">
      <span class="status-label">Content Found:</span>
      <span class="status-value" id="hasContent">No</span>
    </div>
  </div>

  <div class="controls">
    <button class="btn primary" id="toggleBtn" disabled>Toggle Format</button>
    <button class="btn" id="refreshBtn">Refresh Page</button>
  </div>

  <div class="loading" id="loading">
    <div class="spinner"></div>
    <div>Processing...</div>
  </div>

  <div class="info">
    This extension automatically detects and formats escaped Markdown and JSON content on web pages.
  </div>

  <script src="popup.js"></script>
</body>
</html>
