// Popup script for Unescape & Format Chrome Extension

document.addEventListener('DOMContentLoaded', function() {
  const statusEl = document.getElementById('status');
  const contentTypeEl = document.getElementById('contentType');
  const hasContentEl = document.getElementById('hasContent');
  const toggleBtn = document.getElementById('toggleBtn');
  const refreshBtn = document.getElementById('refreshBtn');
  const loadingEl = document.getElementById('loading');

  // Get current tab and check status
  chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
    const currentTab = tabs[0];
    
    // Send message to content script to get status
    chrome.tabs.sendMessage(currentTab.id, { action: 'getStatus' }, function(response) {
      if (chrome.runtime.lastError) {
        // Content script not loaded or page doesn't support it
        updateStatus('Not Available', 'None', false, false);
        return;
      }

      if (response) {
        updateStatus(
          response.isEnabled ? 'Enabled' : 'Disabled',
          response.contentType || 'None',
          response.hasContent,
          response.hasContent
        );
      } else {
        updateStatus('Not Available', 'None', false, false);
      }
    });
  });

  // Toggle button click handler
  toggleBtn.addEventListener('click', function() {
    showLoading(true);
    
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      const currentTab = tabs[0];
      
      chrome.tabs.sendMessage(currentTab.id, { action: 'toggle' }, function(response) {
        showLoading(false);
        
        if (chrome.runtime.lastError) {
          console.error('Error:', chrome.runtime.lastError);
          return;
        }

        if (response && response.success) {
          // Refresh status after toggle
          setTimeout(() => {
            chrome.tabs.sendMessage(currentTab.id, { action: 'getStatus' }, function(statusResponse) {
              if (statusResponse) {
                updateStatus(
                  statusResponse.isEnabled ? 'Enabled' : 'Disabled',
                  statusResponse.contentType || 'None',
                  statusResponse.hasContent,
                  statusResponse.hasContent
                );
              }
            });
          }, 100);
        }
      });
    });
  });

  // Refresh button click handler
  refreshBtn.addEventListener('click', function() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      chrome.tabs.reload(tabs[0].id);
      window.close();
    });
  });

  function updateStatus(status, contentType, hasContent, enableToggle) {
    // Update status
    statusEl.textContent = status;
    statusEl.className = 'status-value ' + (status === 'Enabled' ? 'active' : 
                                           status === 'Disabled' ? 'inactive' : '');

    // Update content type
    contentTypeEl.textContent = contentType.charAt(0).toUpperCase() + contentType.slice(1);

    // Update has content
    hasContentEl.textContent = hasContent ? 'Yes' : 'No';
    hasContentEl.className = 'status-value ' + (hasContent ? 'active' : 'inactive');

    // Enable/disable toggle button
    toggleBtn.disabled = !enableToggle;
    toggleBtn.textContent = status === 'Enabled' ? 'Show Original' : 'Show Formatted';
  }

  function showLoading(show) {
    loadingEl.style.display = show ? 'block' : 'none';
    toggleBtn.style.display = show ? 'none' : 'block';
  }
});
