/* Unescape & Format Chrome Extension Styles */

.unescape-formatter-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  overflow: hidden;
}

.unescape-formatter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e1e5e9;
}

.unescape-formatter-badge {
  font-size: 14px;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.unescape-formatter-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.unescape-formatter-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.unescape-formatter-content {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

/* Markdown Content Styles */
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 10px;
}

.markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 8px;
}

.markdown-content h3 {
  font-size: 1.25em;
}

.markdown-content p {
  margin-bottom: 16px;
}

.markdown-content code {
  background: #f6f8fa;
  border-radius: 3px;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.markdown-content pre {
  background: #f6f8fa;
  border-radius: 6px;
  font-size: 85%;
  line-height: 1.45;
  overflow: auto;
  padding: 16px;
  margin-bottom: 16px;
}

.markdown-content pre code {
  background: transparent;
  border: 0;
  display: inline;
  line-height: inherit;
  margin: 0;
  max-width: auto;
  overflow: visible;
  padding: 0;
  word-wrap: normal;
}

.markdown-content blockquote {
  border-left: 4px solid #dfe2e5;
  color: #6a737d;
  margin: 0 0 16px 0;
  padding: 0 16px;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 16px;
  padding-left: 2em;
}

.markdown-content li {
  margin-bottom: 4px;
}

.markdown-content a {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin-bottom: 16px;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #dfe2e5;
  padding: 6px 13px;
}

.markdown-content table th {
  background: #f6f8fa;
  font-weight: 600;
}

/* JSON Content Styles */
.json-content {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #24292e;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  padding: 16px;
}

/* Scrollbar Styles */
.unescape-formatter-content::-webkit-scrollbar {
  width: 8px;
}

.unescape-formatter-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.unescape-formatter-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.unescape-formatter-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .unescape-formatter-container {
    background: #1a1a1a;
    color: #e1e1e1;
  }

  .markdown-content code,
  .markdown-content pre {
    background: #2d2d2d;
    color: #e1e1e1;
  }

  .json-content {
    background: #2d2d2d !important;
    color: #e1e1e1;
    border-color: #444;
  }

  .markdown-content blockquote {
    border-left-color: #444;
    color: #888;
  }

  .markdown-content table th,
  .markdown-content table td {
    border-color: #444;
  }

  .markdown-content table th {
    background: #2d2d2d;
  }
}
