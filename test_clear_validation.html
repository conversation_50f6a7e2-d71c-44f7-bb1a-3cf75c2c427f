<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-case {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .original, .processed {
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 2px solid #ddd;
        }
        .original {
            background: #ffebee;
            border-color: #f44336;
        }
        .processed {
            background: #e8f5e8;
            border-color: #4caf50;
        }
        .result {
            grid-column: 1 / -1;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        .pass {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .fail {
            background: #ffebee;
            color: #c62828;
        }
        h3 {
            grid-column: 1 / -1;
            margin: 0 0 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 Clear Validation Test</h1>
    <p>Direct comparison of original vs processed content.</p>

    <div class="test-case">
        <h3>Test 1: Simple Header with Trailing Backslash</h3>
        <div class="original" id="orig1"># Hello World\</div>
        <div class="processed" id="proc1"></div>
        <div class="result" id="result1"></div>
    </div>

    <div class="test-case">
        <h3>Test 2: Basic Markdown with Escaped Newlines</h3>
        <div class="original" id="orig2"># Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2</div>
        <div class="processed" id="proc2"></div>
        <div class="result" id="result2"></div>
    </div>

    <div class="test-case">
        <h3>Test 3: Code Block with Escaped Quotes</h3>
        <div class="original" id="orig3">### Code Example\\n\\n```javascript\\nfunction test() {\\n    console.log(\\\"Hello!\\\");\\n}\\n```</div>
        <div class="processed" id="proc3"></div>
        <div class="result" id="result3"></div>
    </div>

    <div class="test-case">
        <h3>Test 4: List Items with Trailing Backslashes</h3>
        <div class="original" id="orig4">- Item 1\\n- Item 2\\n- Item 3\</div>
        <div class="processed" id="proc4"></div>
        <div class="result" id="result4"></div>
    </div>

    <script>
        function unescapeText(text) {
            console.log('🔍 Processing:', text);
            
            let result = text;
            result = result.replace(/\\\\/g, '___DOUBLE_BACKSLASH___');
            result = result
              .replace(/\\n/g, '\n')
              .replace(/\\t/g, '\t')
              .replace(/\\r/g, '\r')
              .replace(/\\"/g, '"')
              .replace(/\\'/g, "'")
              .replace(/\\`/g, '`')
              .replace(/\\#/g, '#')
              .replace(/\\\*/g, '*')
              .replace(/\\\[/g, '[')
              .replace(/\\\]/g, ']')
              .replace(/\\\(/g, '(')
              .replace(/\\\)/g, ')')
              .replace(/\\>/g, '>')
              .replace(/\\-/g, '-')
              .replace(/\\=/g, '=')
              .replace(/\\!/g, '!')
              .replace(/\\&/g, '&')
              .replace(/\\%/g, '%')
              .replace(/\\\$/g, '$')
              .replace(/\\@/g, '@')
              .replace(/\\\^/g, '^')
              .replace(/\\~/g, '~')
              .replace(/\\\+/g, '+')
              .replace(/\\\|/g, '|')
              .replace(/\\{/g, '{')
              .replace(/\\}/g, '}')
              .replace(/\\:/g, ':')
              .replace(/\\;/g, ';')
              .replace(/\\,/g, ',')
              .replace(/\\./g, '.')
              .replace(/\\\?/g, '?');
            result = result.replace(/\\(?![a-zA-Z0-9])/g, '');
            result = result.replace(/___DOUBLE_BACKSLASH___/g, '\\');
            
            console.log('✅ Result:', result);
            return result;
        }

        function runTests() {
            for (let i = 1; i <= 4; i++) {
                const original = document.getElementById(`orig${i}`).textContent;
                const processed = unescapeText(original);
                
                // 显示处理后的内容
                document.getElementById(`proc${i}`).textContent = processed;
                
                // 检查是否还有转义字符（应该检查的是未处理的转义序列）
                const hasUnprocessedEscapes = /\\[ntr"'`#*\[\]()>\-=!&%$@^~+|{}:;,.?]/.test(processed);
                const hasTrailingBackslash = processed.endsWith('\\');
                const hasBackslashN = processed.includes('\\n'); // 这个检查转义的\n，不是真实换行

                const passed = !hasUnprocessedEscapes && !hasTrailingBackslash && !hasBackslashN;
                
                const resultDiv = document.getElementById(`result${i}`);
                resultDiv.className = `result ${passed ? 'pass' : 'fail'}`;
                resultDiv.innerHTML = `
                    <strong>${passed ? 'PASS' : 'FAIL'}</strong><br>
                    Has unprocessed escapes: ${hasUnprocessedEscapes}<br>
                    Has trailing \\: ${hasTrailingBackslash}<br>
                    Has \\n (escaped): ${hasBackslashN}<br>
                    Length: ${original.length} → ${processed.length}
                `;
                
                console.log(`Test ${i}: ${passed ? 'PASS' : 'FAIL'}`);
                console.log(`  Original: "${original}"`);
                console.log(`  Processed: "${processed}"`);
                console.log(`  Has unprocessed escapes: ${hasUnprocessedEscapes}`);
                console.log(`  Has trailing \\: ${hasTrailingBackslash}`);
                console.log(`  Has \\n (escaped): ${hasBackslashN}`);
            }
        }

        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
