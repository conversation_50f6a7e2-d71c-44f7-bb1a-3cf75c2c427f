<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #e9ecef;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .nav {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .nav a {
            margin-right: 15px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        .nav a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="nav">
        <h1>🔧 Unescape & Format Extension Test Suite</h1>
        <a href="#markdown">Markdown Tests</a>
        <a href="#json">JSON Tests</a>
        <a href="#mixed">Mixed Content</a>
        <a href="test_pages/markdown_test.html" target="_blank">Dedicated Markdown Page</a>
        <a href="test_pages/json_test.html" target="_blank">Dedicated JSON Page</a>
    </div>

    <div class="instructions">
        <h3>📋 Testing Instructions</h3>
        <ol>
            <li>Install the Chrome extension first (see INSTALL.md)</li>
            <li>Reload this page after installing the extension</li>
            <li>Click the extension icon to see detection status</li>
            <li>Use "Toggle Format" to switch between original and formatted content</li>
            <li>Test on different sections below</li>
        </ol>
    </div>

    <div id="markdown" class="test-section">
        <h2 class="test-title">📝 Markdown Content Tests</h2>
        
        <h3>Test 1: Basic Markdown with Escape Characters</h3>
        <div class="test-content"># Welcome to My Blog\\n\\n## Introduction\\n\\nThis is a **sample blog post** with some *italic text* and [a link](https://example.com).\\n\\n### Code Example\\n\\n```javascript\\nfunction hello() {\\n    console.log(\\\"Hello, World!\\\");\\n}\\n```\\n\\n### List Items\\n\\n- First item\\n- Second item with **bold text**\\n- Third item\\n\\n> This is a blockquote with some important information.\\n\\nThat\\'s all for now!</div>

        <h3>Test 2: Complex Markdown Structure</h3>
        <div class="test-content">## Getting Started\\n\\nTo use this extension:\\n\\n1. **Install** the extension\\n2. **Navigate** to a page with escaped content\\n3. **Click** the extension icon\\n4. **Enjoy** the formatted content!\\n\\n### Features\\n\\n- ✅ Markdown formatting\\n- ✅ JSON formatting\\n- ✅ Escape character removal\\n- ✅ Toggle between original and formatted\\n\\n```bash\\n# Example command\\nnpm install marked\\n```\\n\\n> **Note**: This extension works best with content that has escape characters like \\\\n, \\\\\\\", etc.\\n\\n### Table Example\\n\\n| Feature | Status | Notes |\\n|---------|--------|-------|\\n| Markdown | ✅ | Fully supported |\\n| JSON | ✅ | With formatting |\\n| Toggle | ✅ | Easy switching |</div>
    </div>

    <div id="json" class="test-section">
        <h2 class="test-title">📊 JSON Content Tests</h2>
        
        <h3>Test 1: Simple JSON Object</h3>
        <div class="test-content">{\"name\": \"John Doe\", \"age\": 30, \"city\": \"New York\", \"hobbies\": [\"reading\", \"swimming\", \"coding\"], \"address\": {\"street\": \"123 Main St\", \"zipcode\": \"10001\"}, \"description\": \"A software developer who loves to code.\\nHe enjoys working on various projects.\\nHis favorite language is JavaScript.\"}</div>

        <h3>Test 2: Complex Nested JSON</h3>
        <div class="test-content">{\\n  \\\"users\\\": [\\n    {\\n      \\\"id\\\": 1,\\n      \\\"name\\\": \\\"Alice Johnson\\\",\\n      \\\"email\\\": \\\"<EMAIL>\\\",\\n      \\\"profile\\\": {\\n        \\\"bio\\\": \\\"Software engineer with 5+ years experience.\\\\nSpecializes in React and Node.js.\\\\nLoves open source contributions.\\\",\\n        \\\"skills\\\": [\\\"JavaScript\\\", \\\"Python\\\", \\\"React\\\", \\\"Node.js\\\"],\\n        \\\"social\\\": {\\n          \\\"twitter\\\": \\\"@alice_codes\\\",\\n          \\\"github\\\": \\\"alice-johnson\\\"\\n        }\\n      }\\n    }\\n  ],\\n  \\\"metadata\\\": {\\n    \\\"total\\\": 1,\\n    \\\"timestamp\\\": \\\"2024-01-15T10:30:00Z\\\",\\n    \\\"api_version\\\": \\\"v1.2.3\\\"\\n  }\\n}</div>

        <h3>Test 3: API Response Format</h3>
        <div class="test-content">{\\\"status\\\": \\\"success\\\", \\\"data\\\": {\\\"products\\\": [{\\\"id\\\": 101, \\\"name\\\": \\\"Laptop\\\", \\\"price\\\": 999.99, \\\"description\\\": \\\"High-performance laptop for developers.\\\\nFeatures: 16GB RAM, 512GB SSD, Intel i7.\\\\nPerfect for coding and design work.\\\", \\\"categories\\\": [\\\"Electronics\\\", \\\"Computers\\\"], \\\"in_stock\\\": true}]}, \\\"pagination\\\": {\\\"current_page\\\": 1, \\\"total_pages\\\": 5, \\\"total_items\\\": 50}}</div>
    </div>

    <div id="mixed" class="test-section">
        <h2 class="test-title">🔀 Mixed Content Tests</h2>
        
        <h3>Test 1: Markdown with JSON Code Block</h3>
        <div class="test-content"># API Documentation\\n\\n## User Endpoint\\n\\nThe user endpoint returns the following JSON structure:\\n\\n```json\\n{\\n  \\\"id\\\": 1,\\n  \\\"name\\\": \\\"John Doe\\\",\\n  \\\"email\\\": \\\"<EMAIL>\\\"\\n}\\n```\\n\\n### Response Fields\\n\\n- **id**: Unique user identifier\\n- **name**: Full name of the user\\n- **email**: User\\'s email address</div>

        <h3>Test 2: Documentation with Escaped Content</h3>
        <div class="test-content">## Configuration\\n\\nTo configure the application, create a config.json file:\\n\\n```json\\n{\\n  \\\"database\\\": {\\n    \\\"host\\\": \\\"localhost\\\",\\n    \\\"port\\\": 5432,\\n    \\\"name\\\": \\\"myapp\\\"\\n  },\\n  \\\"features\\\": {\\n    \\\"auth\\\": true,\\n    \\\"logging\\\": true\\n  }\\n}\\n```\\n\\n> **Important**: Make sure to escape special characters in your JSON configuration.</div>
    </div>

    <script>
        // Add some interactivity for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded. Extension should detect content automatically.');
            
            // Add click handlers to test content
            const testContents = document.querySelectorAll('.test-content');
            testContents.forEach((content, index) => {
                content.addEventListener('click', function() {
                    console.log(`Clicked test content ${index + 1}:`, this.textContent.substring(0, 100) + '...');
                });
            });
        });
    </script>
</body>
</html>
