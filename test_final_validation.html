<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 14px;
        }
        .pass { color: green; font-weight: bold; }
        .fail { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🧪 Final Validation Test</h1>
    <p>Testing specific issues that were reported.</p>

    <div class="test-section">
        <h2>Issue 1: Trailing Backslashes</h2>
        <div class="result" id="result1">Testing...</div>
        <pre id="test1"># Hello World\</pre>
    </div>

    <div class="test-section">
        <h2>Issue 2: JSON vs Markdown Detection</h2>
        <div class="result" id="result2">Testing...</div>
        <pre id="test2">{"name": "Test", "value": "Hello\\nWorld"}</pre>
    </div>

    <div class="test-section">
        <h2>Issue 3: Escaped Quotes in Code</h2>
        <div class="result" id="result3">Testing...</div>
        <pre id="test3">```javascript\\nconsole.log(\\\"Hello!\\\");\\n```</pre>
    </div>

    <div class="test-section">
        <h2>Issue 4: List Items with Trailing Backslashes</h2>
        <div class="result" id="result4">Testing...</div>
        <pre id="test4">- Item 1\\n- Item 2\</pre>
    </div>

    <script>
        // Test the unescape function directly
        function unescapeText(text) {
            let result = text;
            result = result.replace(/\\\\/g, '___DOUBLE_BACKSLASH___');
            result = result
              .replace(/\\n/g, '\n')
              .replace(/\\t/g, '\t')
              .replace(/\\r/g, '\r')
              .replace(/\\"/g, '"')
              .replace(/\\'/g, "'")
              .replace(/\\`/g, '`')
              .replace(/\\#/g, '#')
              .replace(/\\\*/g, '*')
              .replace(/\\\[/g, '[')
              .replace(/\\\]/g, ']')
              .replace(/\\\(/g, '(')
              .replace(/\\\)/g, ')')
              .replace(/\\>/g, '>')
              .replace(/\\-/g, '-')
              .replace(/\\=/g, '=')
              .replace(/\\!/g, '!')
              .replace(/\\&/g, '&')
              .replace(/\\%/g, '%')
              .replace(/\\\$/g, '$')
              .replace(/\\@/g, '@')
              .replace(/\\\^/g, '^')
              .replace(/\\~/g, '~')
              .replace(/\\\+/g, '+')
              .replace(/\\\|/g, '|')
              .replace(/\\{/g, '{')
              .replace(/\\}/g, '}')
              .replace(/\\:/g, ':')
              .replace(/\\;/g, ';')
              .replace(/\\,/g, ',')
              .replace(/\\./g, '.')
              .replace(/\\\?/g, '?');
            result = result.replace(/\\(?![a-zA-Z0-9])/g, '');
            result = result.replace(/___DOUBLE_BACKSLASH___/g, '\\');
            return result;
        }

        function isJsonContent(text) {
            const jsonPatterns = [/\\\"/g, /\\n/g, /\\t/g, /\\r/g, /\\\\/g];
            const hasJsonStructure = /^\s*[\{\[]/.test(text.trim()) && /[\}\]]\s*$/.test(text.trim());
            const hasEscapeChars = jsonPatterns.some(pattern => pattern.test(text));
            return hasJsonStructure && hasEscapeChars;
        }

        function isMarkdownContent(text) {
            // Don't treat JSON as Markdown even if it has escape characters
            if (isJsonContent(text)) {
              return false;
            }

            const markdownPatterns = [
              /\\#\s+/, /\\\*\\\*/, /\\\[.*\\\]\\\(.*\\\)/, /\\`.*\\`/, /\\>/,
              /#\s+.*\\n/, /\*\*.*\*\*.*\\n/, /\\\*/, /\\\[/, /\\`/
            ];

            const hasEscapedNewlines = /\\n/.test(text);
            const hasMarkdownStructure = /#\s+|^\*\*|\[.*\]\(|\`.*\`|^\s*[-*+]\s+|^\s*\d+\.\s+/.test(text);

            return markdownPatterns.some(pattern => pattern.test(text)) ||
                   (hasEscapedNewlines && hasMarkdownStructure);
        }

        // Run tests
        function runTests() {
            // Test 1: Trailing backslashes
            const test1Text = document.getElementById('test1').textContent;
            const result1 = unescapeText(test1Text);
            const pass1 = result1 === '# Hello World' && !result1.includes('\\');
            document.getElementById('result1').innerHTML = `
                <span class="${pass1 ? 'pass' : 'fail'}">${pass1 ? 'PASS' : 'FAIL'}</span><br>
                Input: "${test1Text}"<br>
                Output: "${result1}"<br>
                Expected: "# Hello World"
            `;

            // Test 2: JSON detection
            const test2Text = document.getElementById('test2').textContent;
            const isJson = isJsonContent(test2Text);
            const isMd = isMarkdownContent(test2Text);
            const pass2 = isJson && !isMd;
            document.getElementById('result2').innerHTML = `
                <span class="${pass2 ? 'pass' : 'fail'}">${pass2 ? 'PASS' : 'FAIL'}</span><br>
                Input: "${test2Text}"<br>
                Detected as JSON: ${isJson}<br>
                Detected as Markdown: ${isMd}<br>
                Expected: JSON=true, Markdown=false
            `;

            // Test 3: Escaped quotes
            const test3Text = document.getElementById('test3').textContent;
            const result3 = unescapeText(test3Text);
            const pass3 = result3.includes('console.log("Hello!");') && !result3.includes('\\"');
            document.getElementById('result3').innerHTML = `
                <span class="${pass3 ? 'pass' : 'fail'}">${pass3 ? 'PASS' : 'FAIL'}</span><br>
                Input: "${test3Text}"<br>
                Output: "${result3}"<br>
                Contains unescaped quotes: ${pass3}
            `;

            // Test 4: List items
            const test4Text = document.getElementById('test4').textContent;
            const result4 = unescapeText(test4Text);
            const pass4 = result4.includes('- Item 1\n- Item 2') && !result4.endsWith('\\');
            document.getElementById('result4').innerHTML = `
                <span class="${pass4 ? 'pass' : 'fail'}">${pass4 ? 'PASS' : 'FAIL'}</span><br>
                Input: "${test4Text}"<br>
                Output: "${result4}"<br>
                No trailing backslash: ${!result4.endsWith('\\')}
            `;

            // Summary
            const totalTests = 4;
            const passedTests = [pass1, pass2, pass3, pass4].filter(p => p).length;
            console.log(`✅ Tests passed: ${passedTests}/${totalTests}`);
        }

        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
