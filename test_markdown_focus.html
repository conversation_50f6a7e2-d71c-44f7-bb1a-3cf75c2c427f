<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Focus Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
        .expected {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 14px;
        }
        
        /* Extension styles */
        .unescape-formatter-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
            overflow: hidden;
        }

        .unescape-formatter-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .unescape-formatter-badge {
            font-size: 14px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 20px;
        }

        .unescape-formatter-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .unescape-formatter-content {
            padding: 20px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-content h1 {
            font-size: 2em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
        }

        .markdown-content h3 {
            font-size: 1.25em;
        }

        .markdown-content p {
            margin-bottom: 16px;
        }

        .markdown-content code {
            background: #f6f8fa;
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        }

        .markdown-content pre {
            background: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
            margin-bottom: 16px;
        }

        .markdown-content ul, .markdown-content ol {
            margin-bottom: 16px;
            padding-left: 2em;
        }

        .markdown-content li {
            margin-bottom: 4px;
        }

        .markdown-content table {
            border-collapse: collapse;
            border-spacing: 0;
            width: 100%;
            margin-bottom: 16px;
        }

        .markdown-content table th,
        .markdown-content table td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }

        .markdown-content table th {
            background: #f6f8fa;
            font-weight: 600;
        }

        .markdown-content blockquote {
            border-left: 4px solid #dfe2e5;
            padding: 0 16px;
            color: #6a737d;
            margin: 0 0 16px 0;
        }
    </style>
</head>
<body>
    <h1>📝 Markdown Focus Test</h1>
    <p>Testing Markdown processing with focus on the reported issues.</p>

    <div class="test-section">
        <h2>Test 1: Simple Header with Trailing Backslash</h2>
        <div class="expected">Expected: Clean "Hello World" header without backslash</div>
        <pre># Hello World\</pre>
    </div>

    <div class="test-section">
        <h2>Test 2: Basic Markdown Structure</h2>
        <div class="expected">Expected: Proper headers, bold, italic, and lists</div>
        <pre># Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2</pre>
    </div>

    <div class="test-section">
        <h2>Test 3: Code Block with Escaped Quotes</h2>
        <div class="expected">Expected: Code block with proper quotes (no backslashes)</div>
        <pre>### Code Example\\n\\n```javascript\\nfunction test() {\\n    console.log(\\\"Hello!\\\");\\n}\\n```</pre>
    </div>

    <div class="test-section">
        <h2>Test 4: Complex Markdown with Table</h2>
        <div class="expected">Expected: Full markdown with code, table, and proper formatting</div>
        <pre>## Features\\n\\n1. **Auto-detection**\\n2. **Multi-element processing**\\n3. **Better escape handling**\\n\\n> This should work now!\\n\\n| Col1 | Col2 |\\n|------|------|\\n| A    | B    |</pre>
    </div>

    <div class="test-section">
        <h2>Test 5: List Items with Trailing Backslashes</h2>
        <div class="expected">Expected: Clean list items without trailing backslashes</div>
        <pre>- Item 1\\n- Item 2\\n- Item 3\</pre>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // Markdown-focused extension simulation
        class MarkdownFormatter {
            constructor() {
                this.processAllMarkdownElements();
            }

            unescapeText(text) {
                console.log('🔍 Processing Markdown:', text.substring(0, 100) + '...');
                
                let result = text;
                
                // Step 1: Handle double backslashes first
                result = result.replace(/\\\\/g, '___DOUBLE_BACKSLASH___');
                
                // Step 2: Handle all escape sequences
                result = result
                  .replace(/\\n/g, '\n')
                  .replace(/\\t/g, '\t')
                  .replace(/\\r/g, '\r')
                  .replace(/\\"/g, '"')
                  .replace(/\\'/g, "'")
                  .replace(/\\`/g, '`')
                  .replace(/\\#/g, '#')
                  .replace(/\\\*/g, '*')
                  .replace(/\\\[/g, '[')
                  .replace(/\\\]/g, ']')
                  .replace(/\\\(/g, '(')
                  .replace(/\\\)/g, ')')
                  .replace(/\\>/g, '>')
                  .replace(/\\-/g, '-')
                  .replace(/\\=/g, '=')
                  .replace(/\\!/g, '!')
                  .replace(/\\&/g, '&')
                  .replace(/\\%/g, '%')
                  .replace(/\\\$/g, '$')
                  .replace(/\\@/g, '@')
                  .replace(/\\\^/g, '^')
                  .replace(/\\~/g, '~')
                  .replace(/\\\+/g, '+')
                  .replace(/\\\|/g, '|')
                  .replace(/\\{/g, '{')
                  .replace(/\\}/g, '}')
                  .replace(/\\:/g, ':')
                  .replace(/\\;/g, ';')
                  .replace(/\\,/g, ',')
                  .replace(/\\./g, '.')
                  .replace(/\\\?/g, '?');
                
                // Step 3: Remove remaining single backslashes (especially trailing ones)
                result = result.replace(/\\(?![a-zA-Z0-9])/g, '');
                
                // Step 4: Restore double backslashes
                result = result.replace(/___DOUBLE_BACKSLASH___/g, '\\');
                
                console.log('✅ Markdown Result:', result.substring(0, 100) + '...');
                return result;
            }

            isMarkdownContent(text) {
                // Check for Markdown patterns with escape characters
                const markdownPatterns = [
                  /\\#\s+/,  // Escaped headers
                  /\\\*\\\*/,  // Escaped bold
                  /\\\[.*\\\]\\\(.*\\\)/,  // Escaped links
                  /\\`.*\\`/,  // Escaped code
                  /\\>/,  // Escaped blockquotes
                  /#\s+.*\\n/,  // Headers with escaped newlines
                  /\*\*.*\*\*.*\\n/,  // Bold text with escaped newlines
                  /\\\*/,  // Any escaped asterisk
                  /\\\[/,  // Any escaped bracket
                  /\\`/,   // Any escaped backtick
                ];

                // Check for simple headers with trailing backslashes
                const simpleHeaderWithBackslash = /^#\s+.*\\$/.test(text.trim());

                const hasEscapedNewlines = /\\n/.test(text);
                const hasMarkdownStructure = /#\s+|^\*\*|\[.*\]\(|\`.*\`|^\s*[-*+]\s+|^\s*\d+\.\s+/.test(text);

                return markdownPatterns.some(pattern => pattern.test(text)) ||
                       simpleHeaderWithBackslash ||
                       (hasEscapedNewlines && hasMarkdownStructure);
            }

            processAllMarkdownElements() {
                const elements = document.querySelectorAll('pre');
                let processedCount = 0;

                elements.forEach(element => {
                    const text = element.innerText || element.textContent || '';
                    
                    if (text.length < 10) return;

                    console.log(`Checking element:`, text.substring(0, 100) + '...');

                    if (this.isMarkdownContent(text)) {
                        console.log('✅ Detected Markdown content');
                        this.processElementAsMarkdown(element);
                        processedCount++;
                    } else {
                        console.log('❌ Not detected as Markdown');
                    }
                });

                console.log(`✅ Processed ${processedCount} Markdown elements`);
            }

            processElementAsMarkdown(element) {
                const originalContent = element.innerHTML;
                const rawText = element.innerText || element.textContent;
                const unescapedText = this.unescapeText(rawText);

                try {
                    const htmlContent = marked.parse(unescapedText);
                    
                    const processedContent = `
                        <div class="unescape-formatter-container">
                            <div class="unescape-formatter-header">
                                <span class="unescape-formatter-badge">Markdown Formatted</span>
                                <button class="unescape-formatter-toggle" onclick="toggleElement(this)">
                                    Show Original
                                </button>
                            </div>
                            <div class="unescape-formatter-content markdown-content">
                                ${htmlContent}
                            </div>
                        </div>
                    `;

                    element.setAttribute('data-original-content', originalContent);
                    element.innerHTML = processedContent;
                } catch (error) {
                    console.error('Error processing markdown:', error);
                }
            }
        }

        function toggleElement(button) {
            const container = button.closest('.unescape-formatter-container');
            if (!container) return;

            const parentElement = container.parentElement;
            const originalContent = parentElement.getAttribute('data-original-content');
            
            if (!originalContent) return;

            const isShowingFormatted = button.textContent.includes('Show Original');
            
            if (isShowingFormatted) {
                parentElement.innerHTML = originalContent;
            } else {
                new MarkdownFormatter();
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 Starting Markdown-focused test...');
            new MarkdownFormatter();
        });
    </script>
</body>
</html>
