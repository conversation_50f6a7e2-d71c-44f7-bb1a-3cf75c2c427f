<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Test Page</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        pre {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>JSON Content Test Page</h1>
    <p>This page contains escaped JSON content that should be detected and formatted by the extension.</p>
    
    <pre>{\"name\": \"John Doe\", \"age\": 30, \"city\": \"New York\", \"hobbies\": [\"reading\", \"swimming\", \"coding\"], \"address\": {\"street\": \"123 Main St\", \"zipcode\": \"10001\"}, \"description\": \"A software developer who loves to code.\\nHe enjoys working on various projects.\\nHis favorite language is JavaScript.\"}</pre>

    <h2>Complex JSON Example</h2>
    <pre>{\\n  \\\"users\\\": [\\n    {\\n      \\\"id\\\": 1,\\n      \\\"name\\\": \\\"Alice Johnson\\\",\\n      \\\"email\\\": \\\"<EMAIL>\\\",\\n      \\\"profile\\\": {\\n        \\\"bio\\\": \\\"Software engineer with 5+ years experience.\\\\nSpecializes in React and Node.js.\\\\nLoves open source contributions.\\\",\\n        \\\"skills\\\": [\\\"JavaScript\\\", \\\"Python\\\", \\\"React\\\", \\\"Node.js\\\"],\\n        \\\"social\\\": {\\n          \\\"twitter\\\": \\\"@alice_codes\\\",\\n          \\\"github\\\": \\\"alice-johnson\\\"\\n        }\\n      }\\n    },\\n    {\\n      \\\"id\\\": 2,\\n      \\\"name\\\": \\\"Bob Smith\\\",\\n      \\\"email\\\": \\\"<EMAIL>\\\",\\n      \\\"profile\\\": {\\n        \\\"bio\\\": \\\"Full-stack developer and tech enthusiast.\\\\nBuilds scalable web applications.\\\\nMentor for junior developers.\\\",\\n        \\\"skills\\\": [\\\"Java\\\", \\\"Spring\\\", \\\"Angular\\\", \\\"PostgreSQL\\\"],\\n        \\\"social\\\": {\\n          \\\"linkedin\\\": \\\"bob-smith-dev\\\",\\n          \\\"website\\\": \\\"https://bobsmith.dev\\\"\\n        }\\n      }\\n    }\\n  ],\\n  \\\"metadata\\\": {\\n    \\\"total\\\": 2,\\n    \\\"page\\\": 1,\\n    \\\"timestamp\\\": \\\"2024-01-15T10:30:00Z\\\",\\n    \\\"api_version\\\": \\\"v1.2.3\\\"\\n  }\\n}</pre>

    <h2>API Response Example</h2>
    <pre>{\\\"status\\\": \\\"success\\\", \\\"data\\\": {\\\"products\\\": [{\\\"id\\\": 101, \\\"name\\\": \\\"Laptop\\\", \\\"price\\\": 999.99, \\\"description\\\": \\\"High-performance laptop for developers.\\\\nFeatures: 16GB RAM, 512GB SSD, Intel i7.\\\\nPerfect for coding and design work.\\\", \\\"categories\\\": [\\\"Electronics\\\", \\\"Computers\\\"], \\\"in_stock\\\": true}, {\\\"id\\\": 102, \\\"name\\\": \\\"Wireless Mouse\\\", \\\"price\\\": 29.99, \\\"description\\\": \\\"Ergonomic wireless mouse.\\\\nBattery life: 12 months.\\\\nCompatible with all devices.\\\", \\\"categories\\\": [\\\"Electronics\\\", \\\"Accessories\\\"], \\\"in_stock\\\": false}]}, \\\"pagination\\\": {\\\"current_page\\\": 1, \\\"total_pages\\\": 5, \\\"total_items\\\": 50}}</pre>
</body>
</html>
