<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown Test Page</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        pre {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>Markdown Content Test Page</h1>
    <p>This page contains escaped Markdown content that should be detected and formatted by the extension.</p>

    <pre># Welcome to My Blog\\n\\n## Introduction\\n\\nThis is a **sample blog post** with some *italic text* and [a link](https://example.com).\\n\\n### Code Example\\n\\n```javascript\\nfunction hello() {\\n    console.log(\\\"Hello, <PERSON>!\\\");\\n}\\n```\\n\\n### List Items\\n\\n- First item\\n- Second item with **bold text**\\n- Third item\\n\\n> This is a blockquote with some important information.\\n\\n### Table\\n\\n| Name | Age | City |\\n|------|-----|------|\\n| John | 25  | NYC  |\\n| Jane | 30  | LA   |\\n\\n---\\n\\nThat\\'s all for now!</pre>

    <h2>Another Markdown Example</h2>
    <pre>## Getting Started\\n\\nTo use this extension:\\n\\n1. **Install** the extension\\n2. **Navigate** to a page with escaped content\\n3. **Click** the extension icon\\n4. **Enjoy** the formatted content!\\n\\n### Features\\n\\n- ✅ Markdown formatting\\n- ✅ JSON formatting\\n- ✅ Escape character removal\\n- ✅ Toggle between original and formatted\\n\\n```bash\\n# Example command\\nnpm install marked\\n```\\n\\n> **Note**: This extension works best with content that has escape characters like \\\\n, \\\\\\\", etc.</pre>
</body>
</html>
