<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unescape Logic Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-case {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input {
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .output {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .expected {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .pass { color: green; font-weight: bold; }
        .fail { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🧪 Unescape Logic Test</h1>
    <p>Testing the unescape function directly to identify and fix issues.</p>

    <div id="test-results"></div>

    <script>
        // Copy the unescape function from content.js
        function unescapeText(text) {
            console.log('🔍 Original text:', text.substring(0, 200) + '...');
            
            let result = text;
            
            // Step 1: Handle double backslashes first to preserve them temporarily
            result = result.replace(/\\\\/g, '___DOUBLE_BACKSLASH___');
            
            // Step 2: Handle all other escape sequences
            result = result
              // Common escape sequences
              .replace(/\\n/g, '\n')
              .replace(/\\t/g, '\t')
              .replace(/\\r/g, '\r')
              .replace(/\\"/g, '"')
              .replace(/\\'/g, "'")
              
              // Markdown-specific escapes
              .replace(/\\`/g, '`')
              .replace(/\\#/g, '#')
              .replace(/\\\*/g, '*')
              .replace(/\\\[/g, '[')
              .replace(/\\\]/g, ']')
              .replace(/\\\(/g, '(')
              .replace(/\\\)/g, ')')
              .replace(/\\>/g, '>')
              .replace(/\\-/g, '-')
              .replace(/\\=/g, '=')
              .replace(/\\!/g, '!')
              .replace(/\\&/g, '&')
              .replace(/\\%/g, '%')
              .replace(/\\\$/g, '$')
              .replace(/\\@/g, '@')
              .replace(/\\\^/g, '^')
              .replace(/\\~/g, '~')
              .replace(/\\\+/g, '+')
              .replace(/\\\|/g, '|')
              .replace(/\\{/g, '{')
              .replace(/\\}/g, '}')
              .replace(/\\:/g, ':')
              .replace(/\\;/g, ';')
              .replace(/\\,/g, ',')
              .replace(/\\./g, '.')
              .replace(/\\\?/g, '?');
            
            // Step 3: Remove any remaining single backslashes that appear to be escape attempts
            result = result.replace(/\\(?![a-zA-Z0-9])/g, '');
            
            // Step 4: Restore double backslashes as single backslashes
            result = result.replace(/___DOUBLE_BACKSLASH___/g, '\\');
            
            console.log('✅ Processed text:', result.substring(0, 200) + '...');
            return result;
        }

        // Test cases
        const testCases = [
            {
                name: "Simple header with trailing backslash",
                input: "# Hello World\\",
                expected: "# Hello World"
            },
            {
                name: "Markdown with escaped newlines",
                input: "# Hello World\\n\\nThis is **bold** and *italic* text.\\n\\n- Item 1\\n- Item 2",
                expected: "# Hello World\n\nThis is **bold** and *italic* text.\n\n- Item 1\n- Item 2"
            },
            {
                name: "Code with escaped quotes",
                input: "```javascript\\nfunction test() {\\n    console.log(\\\"Hello!\\\");\\n}\\n```",
                expected: "```javascript\nfunction test() {\n    console.log(\"Hello!\");\n}\n```"
            },
            {
                name: "JSON with escaped quotes",
                input: "{\\\"name\\\": \\\"Test\\\", \\\"value\\\": \\\"Hello\\\\nWorld\\\", \\\"items\\\": [1, 2, 3]}",
                expected: "{\"name\": \"Test\", \"value\": \"Hello\\nWorld\", \"items\": [1, 2, 3]}"
            },
            {
                name: "Header with trailing backslash",
                input: "### Code Example\\",
                expected: "### Code Example"
            },
            {
                name: "List items with trailing backslashes",
                input: "- Item 1\\n- Item 2\\",
                expected: "- Item 1\n- Item 2"
            }
        ];

        // Run tests
        function runTests() {
            const resultsDiv = document.getElementById('test-results');
            let passCount = 0;
            let totalCount = testCases.length;

            testCases.forEach((testCase, index) => {
                const result = unescapeText(testCase.input);
                const passed = result === testCase.expected;
                if (passed) passCount++;

                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                testDiv.innerHTML = `
                    <h3>Test ${index + 1}: ${testCase.name} <span class="${passed ? 'pass' : 'fail'}">${passed ? 'PASS' : 'FAIL'}</span></h3>
                    <div><strong>Input:</strong></div>
                    <div class="input">${escapeHtml(testCase.input)}</div>
                    <div><strong>Expected:</strong></div>
                    <div class="expected">${escapeHtml(testCase.expected)}</div>
                    <div><strong>Actual:</strong></div>
                    <div class="output">${escapeHtml(result)}</div>
                    ${!passed ? `<div style="color: red; margin-top: 10px;"><strong>Difference:</strong> Expected length ${testCase.expected.length}, got ${result.length}</div>` : ''}
                `;
                resultsDiv.appendChild(testDiv);
            });

            // Summary
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-case';
            summaryDiv.innerHTML = `
                <h2>Test Summary</h2>
                <p><strong>${passCount}/${totalCount}</strong> tests passed</p>
                <p class="${passCount === totalCount ? 'pass' : 'fail'}">
                    ${passCount === totalCount ? 'All tests passed! 🎉' : 'Some tests failed. Need to fix the logic.'}
                </p>
            `;
            resultsDiv.appendChild(summaryDiv);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
